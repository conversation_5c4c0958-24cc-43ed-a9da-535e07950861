# 应用端口
VITE_APP_PORT = 9091
# VITE_APP_PORT = 8989

# 代理前缀
VITE_APP_BASE_API = '/dev-api'

# 线上接口地址
# VITE_APP_API_URL = http://vapi.youlai.tech
# 开发接口地址
# VITE_APP_API_URL = http://*************:13704
# VITE_APP_API_URL = http://*************:13704
# VITE_APP_API_URL = http://************:8989
# VITE_APP_API_URL= http://*************:8989
# VITE_APP_API_URL = http://**************:8989
# VITE_APP_API_URL = http://*************:13703
# VITE_APP_API_URL = http://*************:8989
# VITE_APP_API_URL =  http://ja.gkga.cn:30000
# VITE_APP_API_URL =  http://**************:8989  
VITE_APP_API_URL =  http://*************:8989  
# VITE_APP_API_URL =  http://localhost:8989  

# 是否启用 Mock 服务
# VITE_MOCK_DEV_SERVER = true
VITE_MOCK_DEV_SERVER = false




